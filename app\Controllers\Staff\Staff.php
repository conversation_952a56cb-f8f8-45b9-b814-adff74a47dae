<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel as FarmBlockModel;
use App\Models\CropsFarmCropsDataModel as FarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel as FarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel as FarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel as FarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel as FarmMarketingDataModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\GroupingsModel;
use App\Models\ExerciseModel;
use App\Helpers\DummyDataHelper;

class Staff extends BaseController
{
    protected $session;
    protected $groupingsModel;
    protected $districtModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->groupingsModel = new GroupingsModel();
        $this->districtModel = new AdxDistrictModel();
        
        // Verify staff role
        if (session()->get('role') != "user") {
            echo 'Access denied';
            exit;
        }
    }

    public function index()
    {
        // Get dashboard statistics from dummy data
        $stats = DummyDataHelper::getDashboardStats();
        
        $data = [
            'title' => 'Staff Dashboard',
            'page_header' => 'Dashboard',
            'page_desc' => 'Staff Control Panel',
            'page_icon' => 'fa-tachometer-alt',
            'stats' => $stats
        ];
        
        return view('staff/staff_dashboard', $data);
    }

    public function workplan($action = 'manage')
    {
        $data = [
            'title' => 'Work Plans',
            'page_header' => 'Work Plans',
            'page_desc' => 'Manage your work plans',
            'page_icon' => 'fa-calendar-alt'
        ];

        if ($action === 'manage') {
            return view('staff/workplan/manage', $data);
        }

        return redirect()->to(base_url('staff/workplan/manage'));
    }

    /**
     * Switch the current district view without changing the default district setting
     */
    public function switch_district($districtId)
    {
        // Get the district details
        $district = $this->districtModel->find($districtId);
        
        if (!$district) {
            return redirect()->back()->with('error', 'District not found');
        }

        // Check if user has permission for this district
        $hasPermission = false;
        $assignedDistricts = session('assigned_districts');
        
        if (!is_array($assignedDistricts)) {
            return redirect()->back()->with('error', 'No districts assigned to your account');
        }

        foreach($assignedDistricts as $assignedDistrict) {
            if ($assignedDistrict['id'] == $districtId) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            return redirect()->back()->with('error', 'You do not have permission to access this district');
        }

        // Only update the session view data, not the default district setting
        $this->session->set('district_id', $district['id']);
        $this->session->set('district_name', $district['name']);

        return redirect()->back()->with('success', 'Switched view to ' . $district['name'] . ' district');
    }
    
    /**
     * Dashboard method for direct access
     */
    public function dashboard()
    {
        return $this->index();
    }
}
