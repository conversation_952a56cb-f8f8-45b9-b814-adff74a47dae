<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxCountryModel;
use App\Models\EducationModel;
use App\Helpers\DummyDataHelper;

class FarmerController extends BaseController
{
    protected $farmerModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $provinceModel;
    protected $countryModel;
    protected $educationModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->farmerModel = new FarmerInformationModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->countryModel = new AdxCountryModel();
        $this->educationModel = new EducationModel();
    }

    /**
     * Display a listing of farmers
     * GET /staff/farmers
     */
    public function index()
    {
        // Use dummy farmer data
        $farmers = DummyDataHelper::getDummyFarmers();

        $data = [
            'title' => 'Farmers Management',
            'page_header' => 'Farmers Management',
            'farmers' => $farmers
        ];

        return view('staff/farmers/index', $data);
    }

    /**
     * Show the form for creating a new farmer
     * GET /staff/farmers/create
     */
    public function create()
    {
        $data = [
            'title' => 'Add New Farmer',
            'page_header' => 'Add New Farmer',
            'district' => DummyDataHelper::getDummyDistricts()[0],
            'district_name' => session()->get('district_name', 'Port Moresby'),
            'province' => DummyDataHelper::getDummyProvinces()[0],
            'country' => ['id' => 1, 'name' => 'Papua New Guinea'],
            'llgs' => DummyDataHelper::getDummyLLGs(),
            'wards' => DummyDataHelper::getDummyWards(),
            'education_levels' => DummyDataHelper::getDummyEducationLevels(),
            'selected_district_id' => 1
        ];

        return view('staff/farmers/farmers_create', $data);
    }

    /**
     * Store a newly created farmer in storage
     * POST /staff/farmers
     */
    public function store()
    {
        try {
            // Get form data
            $data = $this->request->getPost();

            // Generate farmer code automatically
            $provinceId = $data['province_id'];
            $data['farmer_code'] = $this->farmerModel->generateFarmerCode($provinceId);

            // Handle photo upload
            $photo = $this->request->getFile('id_photo');
            if ($photo && $photo->isValid() && !$photo->hasMoved()) {
                $uploadPath = FCPATH . 'public/uploads/farmer_photos';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Validate file
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $maxSize = 25 * 1024 * 1024; // 25MB

                if (!in_array($photo->getMimeType(), $allowedTypes)) {
                    return redirect()->back()->withInput()
                        ->with('error', 'Invalid file type. Only JPEG, PNG, and GIF files are allowed.');
                }

                if ($photo->getSize() > $maxSize) {
                    return redirect()->back()->withInput()
                        ->with('error', 'File size too large. Maximum size is 25MB.');
                }

                $newName = $photo->getRandomName();
                if ($photo->move($uploadPath, $newName)) {
                    $data['id_photo'] = 'uploads/farmer_photos/' . $newName;
                }
            }

            // Insert farmer
            if ($this->farmerModel->insert($data)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer added successfully.');
            } else {
                $errors = $this->farmerModel->errors();
                return redirect()->back()->withInput()
                    ->with('error', 'Failed to add farmer.')
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Store] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while adding the farmer. Please try again.');
        }
    }

    /**
     * Display the specified farmer
     * GET /staff/farmers/{id}
     */
    public function show($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer) {
                return redirect()->to('staff/farmers')->with('error', 'Farmer not found.');
            }

            // Get location details
            $district = $this->districtModel->find($farmer['district_id']);
            $llg = $this->llgModel->find($farmer['llg_id']);
            $ward = $this->wardModel->find($farmer['ward_id']);
            $province = $this->provinceModel->find($farmer['province_id']);
            $country = $this->countryModel->find($farmer['country_id']);
            $education = $this->educationModel->find($farmer['highest_education_id']);

            $data = [
                'title' => 'Farmer Details',
                'page_header' => 'Farmer Details',
                'farmer' => $farmer,
                'district' => $district,
                'llg' => $llg,
                'ward' => $ward,
                'province' => $province,
                'country' => $country,
                'education' => $education
            ];

            return view('staff/farmers/farmers_show', $data);

        } catch (\Exception $e) {
            log_message('error', '[Farmer Show] ' . $e->getMessage());
            return redirect()->to('staff/farmers')->with('error', 'Error loading farmer details.');
        }
    }

    /**
     * Show the form for editing the specified farmer
     * GET /staff/farmers/{id}/edit
     */
    public function edit($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer) {
                return redirect()->to('staff/farmers')->with('error', 'Farmer not found.');
            }

            // Get location data
            $provinces = $this->provinceModel->findAll();
            $countries = $this->countryModel->findAll();
            $educationLevels = $this->educationModel->findAll();

            // Get districts, LLGs and wards
            $districts = [];
            $llgs = [];
            $wards = [];

            if ($farmer['province_id']) {
                $districts = $this->districtModel->where('province_id', $farmer['province_id'])->findAll();
            }

            if ($farmer['district_id']) {
                $llgs = $this->llgModel->where('district_id', $farmer['district_id'])->findAll();

                // Get all wards for the district
                $llgIds = array_column($llgs, 'id');
                if (!empty($llgIds)) {
                    $wards = $this->wardModel->whereIn('llg_id', $llgIds)->findAll();
                }
            }

            $data = [
                'title' => 'Edit Farmer',
                'page_header' => 'Edit Farmer',
                'farmer' => $farmer,
                'provinces' => $provinces,
                'districts' => $districts,
                'llgs' => $llgs,
                'wards' => $wards,
                'countries' => $countries,
                'education_levels' => $educationLevels
            ];

            return view('staff_farmers/farmers_edit', $data);

        } catch (\Exception $e) {
            log_message('error', '[Farmer Edit] ' . $e->getMessage());
            return redirect()->to('staff/farmers')->with('error', 'Error loading edit form.');
        }
    }

    /**
     * Update the specified farmer in storage
     * PUT/PATCH /staff/farmers/{id}
     * POST /staff/farmers/{id} (for form compatibility)
     */
    public function update($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer) {
                return redirect()->to('staff/farmers')->with('error', 'Farmer not found.');
            }

            // Get form data
            $data = $this->request->getPost();

            // Handle photo upload
            $photo = $this->request->getFile('id_photo');
            if ($photo && $photo->isValid() && !$photo->hasMoved()) {
                $uploadPath = FCPATH . 'public/uploads/farmer_photos';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Validate file
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $maxSize = 25 * 1024 * 1024; // 25MB

                if (!in_array($photo->getMimeType(), $allowedTypes)) {
                    return redirect()->back()->withInput()
                        ->with('error', 'Invalid file type. Only JPEG, PNG, and GIF files are allowed.');
                }

                if ($photo->getSize() > $maxSize) {
                    return redirect()->back()->withInput()
                        ->with('error', 'File size too large. Maximum size is 25MB.');
                }

                // Delete old photo if exists
                if (!empty($farmer['id_photo']) && file_exists(FCPATH . 'public/' . $farmer['id_photo'])) {
                    unlink(FCPATH . 'public/' . $farmer['id_photo']);
                }

                $newName = $photo->getRandomName();
                if ($photo->move($uploadPath, $newName)) {
                    $data['id_photo'] = 'uploads/farmer_photos/' . $newName;
                }
            }

            // Update farmer
            if ($this->farmerModel->update($id, $data)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer updated successfully.');
            } else {
                $errors = $this->farmerModel->errors();
                return redirect()->back()->withInput()
                    ->with('error', 'Failed to update farmer.')
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Update] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while updating the farmer. Please try again.');
        }
    }

    /**
     * Remove the specified farmer from storage
     * DELETE /staff/farmers/{id}
     * GET /staff/farmers/{id}/delete (for link compatibility)
     */
    public function delete($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer) {
                return redirect()->to('staff/farmers')->with('error', 'Farmer not found.');
            }

            // Delete associated photo if exists
            if (!empty($farmer['id_photo']) && file_exists(FCPATH . 'public/' . $farmer['id_photo'])) {
                unlink(FCPATH . 'public/' . $farmer['id_photo']);
            }

            // Soft delete farmer (uses deleted_at timestamp)
            if ($this->farmerModel->delete($id)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer deleted successfully.');
            } else {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Failed to delete farmer. Please try again.');
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Delete] ' . $e->getMessage());
            return redirect()->to('staff/farmers')
                ->with('error', 'An error occurred while deleting the farmer. Please try again.');
        }
    }

}
