<?php

namespace App\Helpers;

class DummyDataHelper
{
    // Authentication dummy data
    public static function getDummyUsers()
    {
        return [
            // Admin users
            [
                'id' => 1,
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'name' => 'System Administrator',
                'role' => 'admin',
                'is_admin' => 1,
                'is_supervisor' => 1,
                'position' => 'System Administrator',
                'status' => 1,
                'org_id' => 1,
                'phone' => '+************'
            ],
            // Staff users
            [
                'id' => 2,
                'email' => '<EMAIL>',
                'password' => password_hash('staff123', PASSWORD_DEFAULT),
                'name' => '<PERSON>',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Field Officer',
                'status' => 1,
                'org_id' => 1,
                'phone' => '+************'
            ],
            // Dakoii users
            [
                'id' => 3,
                'email' => '<EMAIL>',
                'password' => password_hash('dakoii123', PASSWORD_DEFAULT),
                'name' => '<PERSON> <PERSON>',
                'role' => 'dakoii',
                'is_admin' => 1,
                'is_supervisor' => 1,
                'position' => 'System Manager',
                'status' => 1,
                'org_id' => 1,
                'phone' => '+************'
            ]
        ];
    }

    public static function getDummyOrganization()
    {
        return [
            'id' => 1,
            'name' => 'Papua New Guinea Agricultural Department',
            'orglogo' => 'uploads/org_logos/png_agriculture.png',
            'orgcode' => 'PNGAD',
            'is_active' => 1,
            'addlockcountry' => 1,
            'addlockprov' => 1
        ];
    }

    public static function getDummyDistrictPermissions()
    {
        return [
            [
                'user_id' => 2,
                'org_id' => 1,
                'district_id' => 1,
                'default_district' => 1
            ]
        ];
    }

    public static function getDummyDistricts()
    {
        return [
            [
                'id' => 1,
                'name' => 'Port Moresby',
                'province_id' => 1,
                'country_id' => 1
            ],
            [
                'id' => 2,
                'name' => 'Lae',
                'province_id' => 2,
                'country_id' => 1
            ]
        ];
    }

    public static function getDummyProvinces()
    {
        return [
            [
                'id' => 1,
                'name' => 'National Capital District',
                'country_id' => 1,
                'json_id' => 'NCD'
            ],
            [
                'id' => 2,
                'name' => 'Morobe',
                'country_id' => 1,
                'json_id' => 'MOR'
            ]
        ];
    }

    // Agricultural dummy data
    public static function getDummyFarmers()
    {
        return [
            [
                'id' => 1,
                'farmer_code' => 'FRM001',
                'given_name' => 'Peter',
                'surname' => 'Kaupa',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'gender' => 'Male',
                'age' => 35,
                'village' => 'Gerehu',
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'status' => 'active',
                'highest_education_id' => 1,
                'id_photo' => null,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'farmer_code' => 'FRM002',
                'given_name' => 'Maria',
                'surname' => 'Temu',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'gender' => 'Female',
                'age' => 28,
                'village' => '9-Mile',
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'status' => 'active',
                'highest_education_id' => 2,
                'id_photo' => null,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 3,
                'farmer_code' => 'FRM003',
                'given_name' => 'James',
                'surname' => 'Wambi',
                'phone' => '+************',
                'email' => '<EMAIL>',
                'gender' => 'Male',
                'age' => 42,
                'village' => 'Saraga',
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'status' => 'active',
                'highest_education_id' => 3,
                'id_photo' => null,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    public static function getDummyCrops()
    {
        return [
            ['id' => 1, 'name' => 'Sweet Potato', 'category' => 'Root Crop'],
            ['id' => 2, 'name' => 'Banana', 'category' => 'Fruit'],
            ['id' => 3, 'name' => 'Cassava', 'category' => 'Root Crop'],
            ['id' => 4, 'name' => 'Taro', 'category' => 'Root Crop'],
            ['id' => 5, 'name' => 'Yam', 'category' => 'Root Crop'],
            ['id' => 6, 'name' => 'Coconut', 'category' => 'Fruit'],
            ['id' => 7, 'name' => 'Coffee', 'category' => 'Cash Crop'],
            ['id' => 8, 'name' => 'Cocoa', 'category' => 'Cash Crop']
        ];
    }

    public static function getDummyFarmBlocks()
    {
        return [
            [
                'id' => 1,
                'block_code' => 'BLK001',
                'farmer_id' => 1,
                'crop_id' => 1,
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Gerehu',
                'block_site' => 'Gerehu Block A',
                'lon' => '147.1801',
                'lat' => '-9.4438',
                'status' => 'active',
                'created_by' => 2,
                'farmer_name' => 'Peter Kaupa',
                'crop_name' => 'Sweet Potato'
            ],
            [
                'id' => 2,
                'block_code' => 'BLK002',
                'farmer_id' => 2,
                'crop_id' => 2,
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => '9-Mile',
                'block_site' => '9-Mile Gardens',
                'lon' => '147.1950',
                'lat' => '-9.4200',
                'status' => 'active',
                'created_by' => 2,
                'farmer_name' => 'Maria Temu',
                'crop_name' => 'Banana'
            ]
        ];
    }

    public static function getDummyEducationLevels()
    {
        return [
            ['id' => 1, 'level' => 'Primary School'],
            ['id' => 2, 'level' => 'Secondary School'],
            ['id' => 3, 'level' => 'Tertiary Education'],
            ['id' => 4, 'level' => 'No Formal Education']
        ];
    }

    public static function getDummyLLGs()
    {
        return [
            [
                'id' => 1,
                'name' => 'Moresby North-East',
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1
            ],
            [
                'id' => 2,
                'name' => 'Moresby South',
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1
            ]
        ];
    }

    public static function getDummyWards()
    {
        return [
            [
                'id' => 1,
                'name' => 'Gerehu Ward 1',
                'llg_id' => 1,
                'district_id' => 1
            ],
            [
                'id' => 2,
                'name' => '9-Mile Ward',
                'llg_id' => 1,
                'district_id' => 1
            ],
            [
                'id' => 3,
                'name' => 'Saraga Ward',
                'llg_id' => 2,
                'district_id' => 1
            ]
        ];
    }

    // Dashboard statistics
    public static function getDashboardStats()
    {
        return [
            'total_farmers' => 156,
            'active_farms' => 243,
            'total_crops' => 8,
            'harvest_records' => 89,
            'monthly_growth' => 12.5,
            'revenue_trend' => 8.3,
            'recent_activities' => [
                [
                    'action' => 'New farmer registered',
                    'farmer' => 'John Doe',
                    'date' => date('Y-m-d', strtotime('-2 days')),
                    'time' => '14:30'
                ],
                [
                    'action' => 'Harvest recorded',
                    'farmer' => 'Mary Smith',
                    'crop' => 'Sweet Potato',
                    'amount' => '250 kg',
                    'date' => date('Y-m-d', strtotime('-1 day')),
                    'time' => '09:15'
                ],
                [
                    'action' => 'Farm block created',
                    'farmer' => 'Peter Wilson',
                    'location' => 'Gerehu',
                    'date' => date('Y-m-d'),
                    'time' => '11:45'
                ]
            ],
            'crop_distribution' => [
                ['crop' => 'Sweet Potato', 'percentage' => 35],
                ['crop' => 'Banana', 'percentage' => 25],
                ['crop' => 'Cassava', 'percentage' => 20],
                ['crop' => 'Taro', 'percentage' => 12],
                ['crop' => 'Others', 'percentage' => 8]
            ]
        ];
    }

    // Report data
    public static function getFarmersReportData()
    {
        return [
            'summary' => [
                'total_farmers' => 156,
                'active_farmers' => 142,
                'inactive_farmers' => 14,
                'male_farmers' => 89,
                'female_farmers' => 67,
                'average_age' => 34.5
            ],
            'by_education' => [
                ['level' => 'Primary School', 'count' => 78],
                ['level' => 'Secondary School', 'count' => 52],
                ['level' => 'Tertiary Education', 'count' => 18],
                ['level' => 'No Formal Education', 'count' => 8]
            ],
            'by_village' => [
                ['village' => 'Gerehu', 'count' => 45],
                ['village' => '9-Mile', 'count' => 38],
                ['village' => 'Saraga', 'count' => 32],
                ['village' => 'Boroko', 'count' => 25],
                ['village' => 'Others', 'count' => 16]
            ]
        ];
    }

    // Exercise/Project data
    public static function getDummyExercises()
    {
        return [
            [
                'id' => 1,
                'name' => 'PNG Agricultural Development 2024',
                'description' => 'Main agricultural development program for 2024',
                'start_date' => '2024-01-01',
                'end_date' => '2024-12-31',
                'status' => 'active',
                'org_id' => 1
            ]
        ];
    }

    // Authentication helper methods
    public static function authenticateUser($identifier, $password)
    {
        $users = self::getDummyUsers();
        
        foreach ($users as $user) {
            if (($user['email'] === $identifier || $user['id'] == $identifier) && 
                password_verify($password, $user['password'])) {
                return $user;
            }
        }
        
        return null;
    }

    public static function getUserById($id)
    {
        $users = self::getDummyUsers();
        
        foreach ($users as $user) {
            if ($user['id'] == $id) {
                return $user;
            }
        }
        
        return null;
    }
}